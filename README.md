# Playwright Docker Compose 設置 (含 VNC/noVNC)

這個配置創建了兩個 Playwright Docker 實例，集成了 VNC/noVNC 功能，可以通過瀏覽器遠程查看測試執行過程。

## 文件說明

- `docker-compose.yml` - Docker Compose 配置文件
- `Dockerfile.playwright-vnc` - 自定義 Dockerfile，添加 VNC 支持
- `start-vnc.sh` - VNC 啟動腳本
- `supervisord.conf` - Supervisor 配置文件，管理多個進程
- `seccomp_profile.json` - Chromium 沙盒所需的安全配置文件
- `README.md` - 使用說明

## 啟動服務

```bash
# 構建並啟動兩個 Playwright 實例
docker-compose up -d --build

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs
```

## VNC/noVNC 訪問

### 通過瀏覽器訪問 (推薦)

- **實例 1**: <http://localhost:6080> (noVNC Web 界面)
- **實例 2**: <http://localhost:6081> (noVNC Web 界面)

**無需密碼** - 直接連接即可

### 通過 VNC 客戶端訪問

- **實例 1**: `localhost:5901`
- **實例 2**: `localhost:5902`

## 連接到 Playwright 實例

### 實例 1 (端口 3000)

```bash
# 使用環境變量連接
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3000/ npx playwright test
```

### 實例 2 (端口 3001)

```bash
# 使用環境變量連接
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3001/ npx playwright test
```

### 使用 API 連接

```javascript
const { chromium } = require('playwright');

// 連接到實例 1
const browser1 = await chromium.connect('ws://127.0.0.1:3000/');

// 連接到實例 2
const browser2 = await chromium.connect('ws://127.0.0.1:3001/');
```

## 停止服務

```bash
# 停止並移除容器
docker-compose down

# 停止、移除容器並清理網絡
docker-compose down --volumes --remove-orphans
```

## 配置說明

- **映像版本**: 基於 `mcr.microsoft.com/playwright:v1.55.0-noble` 自定義構建
- **用戶**: `pwuser` (非 root 用戶，更安全)
- **端口配置**:
  - 實例 1: Playwright(3001), VNC(5901), noVNC(6081)
  - 實例 2: Playwright(3002), VNC(5902), noVNC(6082)
- **VNC 設置**: 1920x1080 解析度，24位色深
- **安全**: 使用 seccomp profile 支持 Chromium 沙盒
- **網絡**: 自定義橋接網絡用於容器間通信

## 注意事項

1. 確保本地端口沒有被其他服務占用：
   - Playwright: 3001, 3002
   - VNC: 5901, 5902
   - noVNC: 6081, 6082
2. 測試代碼中的 Playwright 版本應該與 Docker 映像中的版本匹配 (v1.55.0)
3. 首次啟動需要構建映像，可能需要幾分鐘時間
4. VNC 連接無需密碼，直接訪問即可

## Python 測試腳本

我們提供了兩個 Python 測試腳本：

### 簡單測試
```bash
# 安裝依賴
pip install -r requirements.txt

# 運行簡單測試 (連接實例 1)
python simple_test.py
```

### 完整測試
```bash
# 運行完整測試 (可選擇實例或同時測試)
python test_google.py
```

測試腳本會：
1. 連接到指定的 Playwright Docker 實例
2. 開啟 Google 網站
3. 等待 10 秒
4. 顯示頁面標題並關閉

## 故障排除

如果遇到問題，可以嘗試：

1. 檢查所有必要文件是否存在
2. 確保 Docker 有足夠的內存分配 (建議至少 4GB)
3. 查看容器日誌：`docker-compose logs playwright-vnc-1`
4. 重新構建映像：`docker-compose build --no-cache`
5. 確認 Playwright 版本匹配：`pip install playwright==1.55.0`
