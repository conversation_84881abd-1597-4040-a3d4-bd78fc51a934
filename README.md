# Playwright Docker Compose 設置

這個配置創建了兩個 Playwright Docker 實例，可以用於並行測試或負載分散。

## 文件說明

- `docker-compose.yml` - Docker Compose 配置文件
- `seccomp_profile.json` - Chromium 沙盒所需的安全配置文件
- `README.md` - 使用說明

## 啟動服務

```bash
# 啟動兩個 Playwright 實例
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs
```

## 連接到 Playwright 實例

### 實例 1 (端口 3000)
```bash
# 使用環境變量連接
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3000/ npx playwright test
```

### 實例 2 (端口 3001)
```bash
# 使用環境變量連接
PW_TEST_CONNECT_WS_ENDPOINT=ws://127.0.0.1:3001/ npx playwright test
```

### 使用 API 連接
```javascript
const { chromium } = require('playwright');

// 連接到實例 1
const browser1 = await chromium.connect('ws://127.0.0.1:3000/');

// 連接到實例 2
const browser2 = await chromium.connect('ws://127.0.0.1:3001/');
```

## 停止服務

```bash
# 停止並移除容器
docker-compose down

# 停止、移除容器並清理網絡
docker-compose down --volumes --remove-orphans
```

## 配置說明

- **映像版本**: `mcr.microsoft.com/playwright:v1.55.0-noble`
- **用戶**: `pwuser` (非 root 用戶，更安全)
- **端口**: 實例 1 使用 3000，實例 2 使用 3001
- **安全**: 使用 seccomp profile 支持 Chromium 沙盒
- **網絡**: 自定義橋接網絡用於容器間通信

## 注意事項

1. 確保本地端口 3000 和 3001 沒有被其他服務占用
2. 測試代碼中的 Playwright 版本應該與 Docker 映像中的版本匹配 (v1.55.0)
3. 如果需要訪問本地服務器，可以在 docker-compose.yml 中添加 `extra_hosts` 配置

## 故障排除

如果遇到 Chromium 啟動問題，可以嘗試：

1. 檢查 seccomp_profile.json 文件是否存在
2. 確保 Docker 有足夠的內存分配
3. 查看容器日誌：`docker-compose logs playwright-instance-1`
