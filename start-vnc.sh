#!/bin/bash

# 設置顯示
export DISPLAY=:99

# 啟動 Xvfb
echo "Starting Xvfb..."
Xvfb :99 -screen 0 ${SCREEN_WIDTH}x${SCREEN_HEIGHT}x${SCREEN_DEPTH} -ac +extension GLX +render -noreset &
XVFB_PID=$!

# 等待 Xvfb 啟動
sleep 2

# 啟動窗口管理器
echo "Starting Fluxbox..."
fluxbox &
FLUXBOX_PID=$!

# 啟動 VNC 服務器
echo "Starting VNC server..."
x11vnc -display :99 -forever -nopw -shared -rfbport ${VNC_PORT} &
VNC_PID=$!

# 啟動 noVNC
echo "Starting noVNC..."
/opt/noVNC/utils/novnc_proxy --vnc localhost:${VNC_PORT} --listen ${NOVNC_PORT} &
NOVNC_PID=$!

# 等待服務啟動
sleep 3

# 啟動 Playwright 服務器
echo "Starting Playwright server on port ${PLAYWRIGHT_PORT}..."
cd /home/<USER>
npx -y playwright@1.55.0 run-server --port ${PLAYWRIGHT_PORT} --host 0.0.0.0 &
PLAYWRIGHT_PID=$!

# 等待所有進程
wait $PLAYWRIGHT_PID
