[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:xvfb]
command=Xvfb :99 -screen 0 %(ENV_SCREEN_WIDTH)sx%(ENV_SCREEN_HEIGHT)sx%(ENV_SCREEN_DEPTH)s -ac +extension GLX +render -noreset
user=pwuser
autostart=true
autorestart=true
priority=100
stdout_logfile=/var/log/supervisor/xvfb.log
stderr_logfile=/var/log/supervisor/xvfb.log

[program:fluxbox]
command=fluxbox
user=pwuser
environment=DISPLAY=":99"
autostart=true
autorestart=true
priority=200
stdout_logfile=/var/log/supervisor/fluxbox.log
stderr_logfile=/var/log/supervisor/fluxbox.log

[program:x11vnc]
command=x11vnc -display :99 -forever -nopw -shared -rfbport %(ENV_VNC_PORT)s
user=pwuser
autostart=true
autorestart=true
priority=300
stdout_logfile=/var/log/supervisor/x11vnc.log
stderr_logfile=/var/log/supervisor/x11vnc.log

[program:novnc]
command=/opt/noVNC/utils/novnc_proxy --vnc localhost:%(ENV_VNC_PORT)s --listen %(ENV_NOVNC_PORT)s
user=pwuser
autostart=true
autorestart=true
priority=400
stdout_logfile=/var/log/supervisor/novnc.log
stderr_logfile=/var/log/supervisor/novnc.log

[program:playwright]
command=bash -c "cd /home/<USER>"
user=pwuser
environment=DISPLAY=":99"
autostart=true
autorestart=true
priority=500
stdout_logfile=/var/log/supervisor/playwright.log
stderr_logfile=/var/log/supervisor/playwright.log
