version: '3.8'

services:
  playwright-instance-1:
    image: mcr.microsoft.com/playwright:v1.55.0-noble
    container_name: playwright-1
    ports:
      - "3000:3000"
    init: true
    ipc: host
    user: pwuser
    working_dir: /home/<USER>
    security_opt:
      - seccomp:./seccomp_profile.json
    command: >
      /bin/sh -c "npx -y playwright@1.55.0 run-server --port 3000 --host 0.0.0.0"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - playwright-network

  playwright-instance-2:
    image: mcr.microsoft.com/playwright:v1.55.0-noble
    container_name: playwright-2
    ports:
      - "3001:3001"
    init: true
    ipc: host
    user: pwuser
    working_dir: /home/<USER>
    security_opt:
      - seccomp:./seccomp_profile.json
    command: >
      /bin/sh -c "npx -y playwright@1.55.0 run-server --port 3001 --host 0.0.0.0"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - playwright-network

networks:
  playwright-network:
    driver: bridge
