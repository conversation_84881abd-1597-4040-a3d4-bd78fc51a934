version: "3.8"

services:
  playwright-instance-1:
    build:
      context: .
      dockerfile: Dockerfile.playwright-vnc
    container_name: playwright-vnc-1
    ports:
      - "3000:3000" # Playwright server
      - "5900:5900" # VNC
      - "6080:6080" # noVNC web interface
    init: true
    ipc: host
    working_dir: /home/<USER>
    security_opt:
      - seccomp:./seccomp_profile.json
    environment:
      - NODE_ENV=production
      - DISPLAY=:99
      - VNC_PORT=5900
      - NOVNC_PORT=6080
      - PLAYWRIGHT_PORT=3000
      - SCREEN_WIDTH=1920
      - SCREEN_HEIGHT=1080
      - SCREEN_DEPTH=24
    restart: unless-stopped
    networks:
      - playwright-network
    volumes:
      - /dev/shm:/dev/shm

  playwright-instance-2:
    build:
      context: .
      dockerfile: Dockerfile.playwright-vnc
    container_name: playwright-vnc-2
    ports:
      - "3001:3000" # Playwright server
      - "5901:5900" # VNC
      - "6081:6080" # noVNC web interface
    init: true
    ipc: host
    working_dir: /home/<USER>
    security_opt:
      - seccomp:./seccomp_profile.json
    environment:
      - NODE_ENV=production
      - DISPLAY=:99
      - VNC_PORT=5900
      - NOVNC_PORT=6080
      - PLAYWRIGHT_PORT=3000
      - SCREEN_WIDTH=1920
      - SCREEN_HEIGHT=1080
      - SCREEN_DEPTH=24
    restart: unless-stopped
    networks:
      - playwright-network
    volumes:
      - /dev/shm:/dev/shm

networks:
  playwright-network:
    driver: bridge
