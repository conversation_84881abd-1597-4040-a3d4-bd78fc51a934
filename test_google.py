#!/usr/bin/env python3
"""
Playwright 測試腳本 - 連接到 Docker 實例並開啟 Google
"""

import asyncio
import time
from playwright.async_api import async_playwright


async def test_google_instance_1():
    """連接到實例 1 並測試 Google"""
    print("🚀 連接到 Playwright 實例 1...")
    
    async with async_playwright() as p:
        try:
            # 連接到 Docker 實例 1
            browser = await p.chromium.connect("ws://127.0.0.1:3001/")
            print("✅ 成功連接到實例 1")
            
            # 創建新頁面
            page = await browser.new_page()
            print("📄 創建新頁面")
            
            # 導航到 Google
            print("🌐 正在開啟 Google...")
            await page.goto("https://www.google.com")
            print("✅ Google 頁面已載入")
            
            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)
            
            # 獲取頁面標題
            title = await page.title()
            print(f"📋 頁面標題: {title}")
            
            # 關閉瀏覽器
            await browser.close()
            print("🔒 瀏覽器已關閉")
            
        except Exception as e:
            print(f"❌ 錯誤: {e}")


async def test_google_instance_2():
    """連接到實例 2 並測試 Google"""
    print("\n🚀 連接到 Playwright 實例 2...")
    
    async with async_playwright() as p:
        try:
            # 連接到 Docker 實例 2
            browser = await p.chromium.connect("ws://127.0.0.1:3002/")
            print("✅ 成功連接到實例 2")
            
            # 創建新頁面
            page = await browser.new_page()
            print("📄 創建新頁面")
            
            # 導航到 Google
            print("🌐 正在開啟 Google...")
            await page.goto("https://www.google.com")
            print("✅ Google 頁面已載入")
            
            # 等待 10 秒
            print("⏰ 等待 10 秒...")
            await asyncio.sleep(10)
            
            # 獲取頁面標題
            title = await page.title()
            print(f"📋 頁面標題: {title}")
            
            # 關閉瀏覽器
            await browser.close()
            print("🔒 瀏覽器已關閉")
            
        except Exception as e:
            print(f"❌ 錯誤: {e}")


async def test_both_instances():
    """同時測試兩個實例"""
    print("🔄 同時測試兩個實例...")
    
    # 並行執行兩個測試
    await asyncio.gather(
        test_google_instance_1(),
        test_google_instance_2()
    )


def main():
    """主函數"""
    print("🎭 Playwright Docker 測試腳本")
    print("=" * 50)
    
    # 選擇測試模式
    print("選擇測試模式:")
    print("1. 測試實例 1")
    print("2. 測試實例 2") 
    print("3. 同時測試兩個實例")
    
    choice = input("請輸入選擇 (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(test_google_instance_1())
    elif choice == "2":
        asyncio.run(test_google_instance_2())
    elif choice == "3":
        asyncio.run(test_both_instances())
    else:
        print("❌ 無效選擇")
        return
    
    print("\n✨ 測試完成!")


if __name__ == "__main__":
    main()
