#!/usr/bin/env python3
"""
簡單的 Playwright 測試腳本 - 連接到實例 1
"""

import asyncio
from playwright.async_api import async_playwright


async def main():
    print("🚀 連接到 Playwright Docker 實例...")
    
    async with async_playwright() as p:
        # 連接到 Docker 實例 1 (端口 3001)
        browser = await p.chromium.connect("ws://127.0.0.1:3001/")
        print("✅ 成功連接!")
        
        # 創建新頁面
        page = await browser.new_page()
        
        # 開啟 Google
        print("🌐 正在開啟 Google...")
        await page.goto("https://www.google.com")
        print("✅ Google 已載入!")
        
        # 等待 10 秒
        print("⏰ 等待 10 秒...")
        await asyncio.sleep(10)
        
        print("✨ 完成!")
        await browser.close()


if __name__ == "__main__":
    asyncio.run(main())
